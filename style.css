/* CSS VARIABLES FOR EASY THEME CUSTOMIZATION */
:root {
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --bg-color: #1e1e1e;
    --panel-color: #2d2d2d;
    --panel-darker-color: #252526;
    --border-color: #444;
    --text-color: #cccccc;
    --text-color-dark: #888;
    --accent-color: #0e639c;
    --active-color: #33a34a;
    --edited-color: #d94f4f;
    --panel-border-radius: 8px;
}

/* BASIC RESET & BODY STYLES */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-color);
    margin: 1rem;
}

.app-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: calc(100vh - 2rem);
    width: 100%;
}

/* HEADER STYLES */
.app-header {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: var(--panel-darker-color);
    border-radius: var(--panel-border-radius);
}

.header-btn {
    background-color: #3c3c3c;
    color: var(--text-color);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}
.header-btn:hover {
    background-color: #4f4f4f;
}

/* MAIN LAYOUT (SIDEBAR + WORKSPACE) */
.main-content {
    display: grid;
    grid-template-columns: 180px 1fr; /* Sidebar width + remaining space */
    gap: 1rem;
    flex-grow: 1;
    min-height: 0; /* Important for flex/grid children with scrolling */
}

/* IMAGE SIDEBAR */
.image-sidebar {
    background-color: var(--panel-darker-color);
    border-radius: var(--panel-border-radius);
    padding: 0.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.thumbnail {
    width: 148px;
    height: 148px;
    margin: 0 auto;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.2s;
    position: relative;
    flex-shrink: 0; /* Prevent thumbnails from shrinking */
}
.thumbnail:hover {
    border-color: #555;
}
.thumbnail.active {
    border-color: var(--active-color);
}
.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #3a3a3a, #4a4a4a);
    border-radius: 4px;
}
/* Status dots */
.thumbnail::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 1px solid var(--bg-color);
}
.thumbnail.edited::after {
    background-color: var(--edited-color);
}
.thumbnail.active::after {
    background-color: var(--active-color);
}

/* EDITOR WORKSPACE */
.editor-workspace {
    background-color: var(--panel-color);
    border-radius: var(--panel-border-radius);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-name-display {
    background-color: var(--panel-darker-color);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-size: 0.9rem;
}

.preview-box {
    width: 48px;
    height: 48px;
    background-color: var(--panel-darker-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* STAGE (TOOLBAR + CANVAS) */
.stage {
    flex-grow: 1;
    display: grid;
    grid-template-columns: 60px 1fr 60px; /* Toolbar | Canvas | Actions */
    gap: 1rem;
    min-height: 0;
}

/* TOOLBAR */
.toolbar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.tool-btn {
    width: 44px;
    height: 44px;
    border-radius: 6px;
    background-color: var(--panel-darker-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    display: grid;
    place-items: center;
    transition: all 0.2s;
}
.tool-btn:hover {
    background-color: #444;
}
.tool-btn.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.color-swatch-container {
    margin-top: auto; /* Pushes color swatch to the bottom */
    padding: 4px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
}
.color-swatch {
    width: 34px;
    height: 34px;
    background-color: var(--active-color);
    border-radius: 4px;
}

/* CANVAS AREA */
.canvas-container {
    background-color: var(--panel-darker-color);
    border-radius: var(--panel-border-radius);
    display: grid;
    place-items: center;
    padding: 1rem;
}

.canvas-placeholder {
    width: 512px;
    height: 512px;
    max-width: 100%;
    max-height: 100%;
    background-color: #fff;
    /* Checkerboard pattern for transparency */
    background-image: 
        linear-gradient(45deg, #ccc 25%, transparent 25%),
        linear-gradient(-45deg, #ccc 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #ccc 75%),
        linear-gradient(-45deg, transparent 75%, #ccc 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
}

/* CANVAS ACTIONS (UNDO/REDO) */
.canvas-actions {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.action-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    color: var(--text-color-dark);
    cursor: pointer;
    display: grid;
    place-items: center;
    transition: all 0.2s;
}
.action-btn:hover {
    background-color: #3c3c3c;
    color: var(--text-color);
}
