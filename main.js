const { app, BrowserWindow, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    titleBarStyle: 'hiddenInset',
    show: false
  });

  mainWindow.loadFile('index.html');

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Always open DevTools for debugging
  mainWindow.webContents.openDevTools();
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for file dialogs
ipcMain.handle('show-open-folder-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory'],
    title: 'Select folder containing PNG images'
  });
  
  if (!result.canceled && result.filePaths.length > 0) {
    const folderPath = result.filePaths[0];
    
    // Read folder and filter for PNG files
    try {
      const files = fs.readdirSync(folderPath);
      const pngFiles = files.filter(file => {
        const filePath = path.join(folderPath, file);
        const stats = fs.statSync(filePath);
        return stats.isFile() && path.extname(file).toLowerCase() === '.png';
      });
      
      return {
        folderPath,
        pngFiles
      };
    } catch (error) {
      console.error('Error reading folder:', error);
      return null;
    }
  }
  
  return null;
});

ipcMain.handle('show-save-project-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    title: 'Save Project',
    defaultPath: 'pixel-project.sqlite',
    filters: [
      { name: 'SQLite Database', extensions: ['sqlite'] }
    ]
  });
  
  return result.canceled ? null : result.filePath;
});

ipcMain.handle('show-open-project-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    title: 'Open Project',
    filters: [
      { name: 'SQLite Database', extensions: ['sqlite'] }
    ],
    properties: ['openFile']
  });
  
  return result.canceled ? null : result.filePaths[0];
});
