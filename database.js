const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

class ProjectDatabase {
  constructor() {
    this.db = null;
    this.projectId = null;
  }

  // Create a new project database
  createProject(dbPath, projectName, folderPath) {
    return new Promise((resolve, reject) => {
      try {
        // Create database
        this.db = new sqlite3.Database(dbPath, (err) => {
          if (err) {
            console.error('Error creating database:', err);
            resolve(false);
            return;
          }

          // Create tables
          this.db.serialize(() => {
            this.db.run(`
              CREATE TABLE projects (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                created DATETIME DEFAULT CURRENT_TIMESTAMP,
                folder_path TEXT
              )
            `);

            this.db.run(`
              CREATE TABLE images (
                id INTEGER PRIMARY KEY,
                project_id INTEGER,
                filename TEXT NOT NULL,
                original_data BLOB,
                current_data BLOB,
                edited BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (project_id) REFERENCES projects (id)
              )
            `);

            // Insert project record
            this.db.run(
              `INSERT INTO projects (name, folder_path) VALUES (?, ?)`,
              [projectName, folderPath],
              function(err) {
                if (err) {
                  console.error('Error inserting project:', err);
                  resolve(false);
                  return;
                }
                this.projectId = this.lastID;
                resolve(true);
              }.bind(this)
            );
          });
        });
      } catch (error) {
        console.error('Error creating project:', error);
        resolve(false);
      }
    });
  }

  // Open existing project
  openProject(dbPath) {
    return new Promise((resolve, reject) => {
      try {
        this.db = new sqlite3.Database(dbPath, (err) => {
          if (err) {
            console.error('Error opening database:', err);
            resolve(null);
            return;
          }

          // Get project info
          this.db.get('SELECT * FROM projects LIMIT 1', (err, project) => {
            if (err) {
              console.error('Error getting project:', err);
              resolve(null);
              return;
            }

            if (project) {
              this.projectId = project.id;
              resolve(project);
            } else {
              resolve(null);
            }
          });
        });
      } catch (error) {
        console.error('Error opening project:', error);
        resolve(null);
      }
    });
  }

  // Add image to project
  addImage(filename, originalData, currentData = null) {
    return new Promise((resolve) => {
      if (!this.db || !this.projectId) {
        resolve(false);
        return;
      }

      try {
        const edited = currentData !== null;
        const dataToStore = currentData || originalData;

        this.db.run(
          `INSERT INTO images (project_id, filename, original_data, current_data, edited) VALUES (?, ?, ?, ?, ?)`,
          [this.projectId, filename, originalData, dataToStore, edited],
          function(err) {
            if (err) {
              console.error('Error adding image:', err);
              resolve(false);
              return;
            }
            resolve(true);
          }
        );
      } catch (error) {
        console.error('Error adding image:', error);
        resolve(false);
      }
    });
  }

  // Update image data
  updateImage(filename, currentData) {
    return new Promise((resolve) => {
      if (!this.db || !this.projectId) {
        resolve(false);
        return;
      }

      try {
        this.db.run(
          `UPDATE images SET current_data = ?, edited = 1 WHERE project_id = ? AND filename = ?`,
          [currentData, this.projectId, filename],
          function(err) {
            if (err) {
              console.error('Error updating image:', err);
              resolve(false);
              return;
            }
            resolve(true);
          }
        );
      } catch (error) {
        console.error('Error updating image:', error);
        resolve(false);
      }
    });
  }

  // Get all images for current project
  getImages() {
    return new Promise((resolve) => {
      if (!this.db || !this.projectId) {
        resolve([]);
        return;
      }

      try {
        this.db.all(
          `SELECT * FROM images WHERE project_id = ? ORDER BY filename`,
          [this.projectId],
          (err, rows) => {
            if (err) {
              console.error('Error getting images:', err);
              resolve([]);
              return;
            }
            resolve(rows || []);
          }
        );
      } catch (error) {
        console.error('Error getting images:', error);
        resolve([]);
      }
    });
  }

  // Get specific image
  getImage(filename) {
    return new Promise((resolve) => {
      if (!this.db || !this.projectId) {
        resolve(null);
        return;
      }

      try {
        this.db.get(
          `SELECT * FROM images WHERE project_id = ? AND filename = ?`,
          [this.projectId, filename],
          (err, row) => {
            if (err) {
              console.error('Error getting image:', err);
              resolve(null);
              return;
            }
            resolve(row || null);
          }
        );
      } catch (error) {
        console.error('Error getting image:', error);
        resolve(null);
      }
    });
  }

  // Revert image to original
  revertImage(filename) {
    return new Promise((resolve) => {
      if (!this.db || !this.projectId) {
        resolve(false);
        return;
      }

      try {
        this.db.run(
          `UPDATE images SET current_data = original_data, edited = 0 WHERE project_id = ? AND filename = ?`,
          [this.projectId, filename],
          function(err) {
            if (err) {
              console.error('Error reverting image:', err);
              resolve(false);
              return;
            }
            resolve(true);
          }
        );
      } catch (error) {
        console.error('Error reverting image:', error);
        resolve(false);
      }
    });
  }

  // Close database
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('Error closing database:', err);
        }
      });
      this.db = null;
      this.projectId = null;
    }
  }

  // Get project info
  getProjectInfo() {
    return new Promise((resolve) => {
      if (!this.db || !this.projectId) {
        resolve(null);
        return;
      }

      try {
        this.db.get(
          'SELECT * FROM projects WHERE id = ?',
          [this.projectId],
          (err, row) => {
            if (err) {
              console.error('Error getting project info:', err);
              resolve(null);
              return;
            }
            resolve(row || null);
          }
        );
      } catch (error) {
        console.error('Error getting project info:', error);
        resolve(null);
      }
    });
  }
}

module.exports = ProjectDatabase;
