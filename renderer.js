const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');
const ProjectDatabase = require('./database');

// Global state
let currentProject = null;
let currentImage = null;
let loadedImages = [];
let currentTool = 'pencil';
let currentColor = '#000000';
let isDrawing = false;
let lastDrawnPixel = null;
let saveTimeout = null;
let editCount = 0;
let toggleViewActive = false;
let originalImageData = null;

// Canvas and context
let canvas = null;
let ctx = null;
let previewCanvas = null;
let previewCtx = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
});

function initializeApp() {
    // Get canvas elements
    canvas = document.getElementById('editor-canvas');
    ctx = canvas.getContext('2d');
    
    // Create preview canvas
    previewCanvas = document.createElement('canvas');
    previewCanvas.width = 48;
    previewCanvas.height = 48;
    previewCtx = previewCanvas.getContext('2d');
    
    // Set up canvas context
    ctx.imageSmoothingEnabled = false;
    previewCtx.imageSmoothingEnabled = false;
    
    // Hide canvas initially
    canvas.style.display = 'none';
    
    // Set up event listeners
    setupEventListeners();
    
    // Initialize UI
    updateColorSwatch();
}

function setupEventListeners() {
    // Header buttons
    document.getElementById('load-folder-btn').addEventListener('click', loadFolder);
    document.getElementById('open-project-btn').addEventListener('click', openProject);
    document.getElementById('save-project-btn').addEventListener('click', saveProject);
    
    // Tool buttons
    document.getElementById('pencil-tool').addEventListener('click', () => setTool('pencil'));
    document.getElementById('fill-tool').addEventListener('click', () => setTool('fill'));
    document.getElementById('toggle-view-tool').addEventListener('click', toggleView);
    
    // Color picker
    const colorPicker = document.getElementById('color-picker');
    colorPicker.addEventListener('change', (e) => {
        currentColor = e.target.value;
        updateColorSwatch();
    });
    
    // Canvas events
    canvas.addEventListener('mousedown', onCanvasMouseDown);
    canvas.addEventListener('mousemove', onCanvasMouseMove);
    canvas.addEventListener('mouseup', onCanvasMouseUp);
    canvas.addEventListener('mouseleave', onCanvasMouseUp);
    
    // Revert button
    document.getElementById('revert-btn').addEventListener('click', revertCurrentImage);
}

async function loadFolder() {
    try {
        const result = await ipcRenderer.invoke('show-open-folder-dialog');
        if (!result) return;
        
        const { folderPath, pngFiles } = result;
        
        if (pngFiles.length === 0) {
            alert('No PNG files found in the selected folder.');
            return;
        }
        
        // Prompt for project name and save location
        const projectPath = await ipcRenderer.invoke('show-save-project-dialog');
        if (!projectPath) return;
        
        const projectName = path.basename(projectPath, '.sqlite');
        
        // Create new project
        currentProject = new ProjectDatabase();
        const created = await currentProject.createProject(projectPath, projectName, folderPath);
        if (!created) {
            alert('Failed to create project.');
            return;
        }

        // Load images
        await loadImagesFromFolder(folderPath, pngFiles);
        
        // Update UI
        updateSidebar();
        
    } catch (error) {
        console.error('Error loading folder:', error);
        alert('Error loading folder: ' + error.message);
    }
}

async function loadImagesFromFolder(folderPath, pngFiles) {
    loadedImages = [];
    
    for (const filename of pngFiles) {
        try {
            const filePath = path.join(folderPath, filename);
            const imageData = fs.readFileSync(filePath);
            
            // Validate image size (basic check)
            const img = new Image();
            img.src = 'data:image/png;base64,' + imageData.toString('base64');
            
            await new Promise((resolve, reject) => {
                img.onload = async () => {
                    if (img.width === 48 && img.height === 48) {
                        console.log(`Loading ${filename}:`);
                        console.log(`- Dimensions: ${img.width}x${img.height}`);
                        console.log(`- File size: ${imageData.length} bytes`);

                        // Create a temporary canvas to analyze the image
                        const tempCanvas = document.createElement('canvas');
                        tempCanvas.width = 48;
                        tempCanvas.height = 48;
                        const tempCtx = tempCanvas.getContext('2d');
                        tempCtx.drawImage(img, 0, 0);

                        // Sample some pixels to see color variations
                        const imageDataSample = tempCtx.getImageData(0, 0, 48, 48);
                        const uniqueColors = new Set();

                        // Sample every 4th pixel to get an idea of color diversity
                        for (let i = 0; i < imageDataSample.data.length; i += 16) { // Every 4th pixel
                            const r = imageDataSample.data[i];
                            const g = imageDataSample.data[i + 1];
                            const b = imageDataSample.data[i + 2];
                            const a = imageDataSample.data[i + 3];
                            uniqueColors.add(`${r},${g},${b},${a}`);
                        }

                        console.log(`- Unique colors sampled: ${uniqueColors.size} (from ${48*48/4} pixels sampled)`);
                        console.log(`- First few colors:`, Array.from(uniqueColors).slice(0, 5));

                        // Add to database
                        await currentProject.addImage(filename, imageData);

                        // Add to loaded images
                        loadedImages.push({
                            filename,
                            originalData: imageData,
                            currentData: imageData,
                            edited: false,
                            element: img
                        });
                        resolve();
                    } else {
                        console.warn(`Skipping ${filename}: not 48x48 pixels (${img.width}x${img.height})`);
                        resolve();
                    }
                };
                img.onerror = reject;
            });
            
        } catch (error) {
            console.error(`Error loading ${filename}:`, error);
        }
    }
}

async function openProject() {
    try {
        console.log('Opening project...');
        const projectPath = await ipcRenderer.invoke('show-open-project-dialog');
        console.log('Selected project path:', projectPath);

        if (!projectPath) return;

        // Close current project
        if (currentProject) {
            console.log('Closing current project');
            currentProject.close();
        }

        // Open project
        console.log('Creating new database instance');
        currentProject = new ProjectDatabase();
        const projectInfo = await currentProject.openProject(projectPath);
        console.log('Project info:', projectInfo);

        if (!projectInfo) {
            alert('Failed to open project.');
            return;
        }

        // Load images from database
        console.log('Loading images from database');
        await loadImagesFromDatabase();
        console.log('Loaded images:', loadedImages.length);

        // Update UI
        updateSidebar();
        console.log('Project opened successfully');

    } catch (error) {
        console.error('Error opening project:', error);
        alert('Error opening project: ' + error.message);
    }
}

async function loadImagesFromDatabase() {
    const images = await currentProject.getImages();
    loadedImages = [];

    for (const imageRecord of images) {
        try {
            const img = new Image();
            const dataUrl = 'data:image/png;base64,' + imageRecord.current_data.toString('base64');
            img.src = dataUrl;
            
            await new Promise((resolve) => {
                img.onload = () => {
                    loadedImages.push({
                        filename: imageRecord.filename,
                        originalData: imageRecord.original_data,
                        currentData: imageRecord.current_data,
                        edited: imageRecord.edited,
                        element: img
                    });
                    resolve();
                };
            });
            
        } catch (error) {
            console.error(`Error loading ${imageRecord.filename} from database:`, error);
        }
    }
}

function saveProject() {
    if (!currentProject) {
        alert('No project loaded.');
        return;
    }
    
    // Save is automatic, just show confirmation
    alert('Project saved successfully.');
}

function updateSidebar() {
    const sidebar = document.getElementById('image-sidebar');
    sidebar.innerHTML = '';
    
    loadedImages.forEach((imageData, index) => {
        const thumbnail = document.createElement('div');
        thumbnail.className = 'thumbnail';
        if (imageData.edited) {
            thumbnail.classList.add('edited');
        }
        
        // Create thumbnail image
        const thumbnailImg = document.createElement('img');
        thumbnailImg.src = imageData.element.src;
        thumbnailImg.style.width = '100%';
        thumbnailImg.style.height = '100%';
        thumbnailImg.style.objectFit = 'contain';
        thumbnailImg.style.imageRendering = 'pixelated';
        
        thumbnail.appendChild(thumbnailImg);
        
        // Click handler
        thumbnail.addEventListener('click', () => selectImage(index));
        
        sidebar.appendChild(thumbnail);
    });
}

function selectImage(index) {
    if (index < 0 || index >= loadedImages.length) return;
    
    // Update current image
    currentImage = index;
    
    // Update sidebar selection
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach((thumb, i) => {
        thumb.classList.toggle('active', i === index);
    });
    
    // Load image to canvas
    loadImageToCanvas(loadedImages[index]);
    
    // Update file name display
    document.getElementById('file-name-display').textContent = loadedImages[index].filename;
    
    // Show canvas
    canvas.style.display = 'block';
    document.getElementById('canvas-placeholder').style.display = 'none';
}

function loadImageToCanvas(imageData) {
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw image scaled to 384x384 (8x scale from 48x48)
    ctx.drawImage(imageData.element, 0, 0, 384, 384);
    
    // Store original image data for toggle view
    originalImageData = ctx.getImageData(0, 0, 384, 384);
    
    // Update preview
    updatePreview();
}

function updatePreview() {
    if (currentImage === null) return;
    
    // Clear preview canvas
    previewCtx.clearRect(0, 0, 48, 48);
    
    // Draw current canvas scaled down to 48x48
    previewCtx.drawImage(canvas, 0, 0, 48, 48);
    
    // Update preview box
    const previewBox = document.getElementById('preview-box');
    previewBox.innerHTML = '';
    
    const previewImg = document.createElement('img');
    previewImg.src = previewCanvas.toDataURL();
    previewImg.style.width = '100%';
    previewImg.style.height = '100%';
    previewImg.style.imageRendering = 'pixelated';
    
    previewBox.appendChild(previewImg);
}

function setTool(tool) {
    currentTool = tool;
    
    // Update tool buttons
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(tool + '-tool').classList.add('active');
    
    // Update cursor
    if (tool === 'pencil') {
        canvas.style.cursor = 'crosshair';
    } else if (tool === 'fill') {
        canvas.style.cursor = 'pointer';
    }
}

function updateColorSwatch() {
    document.getElementById('color-swatch').style.backgroundColor = currentColor;
}

function toggleView() {
    if (!originalImageData) return;

    toggleViewActive = !toggleViewActive;

    const toggleBtn = document.getElementById('toggle-view-tool');
    toggleBtn.classList.toggle('active', toggleViewActive);

    if (toggleViewActive) {
        // Show original image overlay
        const currentImageData = ctx.getImageData(0, 0, 384, 384);

        // Blend original with current (50% opacity)
        const blendedData = ctx.createImageData(384, 384);
        for (let i = 0; i < currentImageData.data.length; i += 4) {
            blendedData.data[i] = (currentImageData.data[i] + originalImageData.data[i]) / 2;     // R
            blendedData.data[i + 1] = (currentImageData.data[i + 1] + originalImageData.data[i + 1]) / 2; // G
            blendedData.data[i + 2] = (currentImageData.data[i + 2] + originalImageData.data[i + 2]) / 2; // B
            blendedData.data[i + 3] = Math.max(currentImageData.data[i + 3], originalImageData.data[i + 3]); // A
        }

        ctx.putImageData(blendedData, 0, 0);
    } else {
        // Restore current image
        if (currentImage !== null) {
            loadImageToCanvas(loadedImages[currentImage]);
        }
    }
}

// Canvas drawing events
function onCanvasMouseDown(e) {
    console.log('Canvas mouse down - currentTool:', currentTool);

    if (currentImage === null) {
        console.log('No current image selected');
        return;
    }

    const rect = canvas.getBoundingClientRect();
    const x = Math.floor((e.clientX - rect.left) / 8); // Convert to 48x48 pixel coordinates
    const y = Math.floor((e.clientY - rect.top) / 8);

    console.log('Click coordinates:', { x, y, currentTool });

    if (x < 0 || x >= 48 || y < 0 || y >= 48) {
        console.log('Click outside bounds');
        return;
    }

    // Handle option+click for color sampling
    if (e.altKey) {
        console.log('Color sampling');
        sampleColor(x, y);
        return;
    }

    if (currentTool === 'pencil') {
        console.log('Using pencil tool');
        isDrawing = true;
        drawPixel(x, y);
        lastDrawnPixel = { x, y };
    } else if (currentTool === 'fill') {
        console.log('Using fill tool');
        floodFill(x, y);
    } else {
        console.log('Unknown tool:', currentTool);
    }
}

function onCanvasMouseMove(e) {
    if (!isDrawing || currentImage === null || currentTool !== 'pencil') return;

    const rect = canvas.getBoundingClientRect();
    const x = Math.floor((e.clientX - rect.left) / 8);
    const y = Math.floor((e.clientY - rect.top) / 8);

    if (x < 0 || x >= 48 || y < 0 || y >= 48) return;

    // Only draw if we moved to a different pixel
    if (!lastDrawnPixel || lastDrawnPixel.x !== x || lastDrawnPixel.y !== y) {
        drawPixel(x, y);
        lastDrawnPixel = { x, y };
    }
}

function onCanvasMouseUp() {
    if (isDrawing) {
        isDrawing = false;
        lastDrawnPixel = null;

        // Trigger save after drawing
        scheduleAutoSave();
    }
}

function drawPixel(x, y) {
    // Draw 8x8 pixel block on 384x384 canvas
    ctx.fillStyle = currentColor;
    ctx.fillRect(x * 8, y * 8, 8, 8);

    // Update preview and mark as edited
    updatePreview();
    markImageAsEdited();
}

function sampleColor(x, y) {
    // Get pixel data from the 48x48 representation
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = 48;
    tempCanvas.height = 48;
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.imageSmoothingEnabled = false;

    // Draw current canvas scaled down
    tempCtx.drawImage(canvas, 0, 0, 48, 48);

    // Get pixel color
    const imageData = tempCtx.getImageData(x, y, 1, 1);
    const [r, g, b] = imageData.data;

    // Convert to hex
    const hex = '#' + [r, g, b].map(x => {
        const hex = x.toString(16);
        return hex.length === 1 ? '0' + hex : hex;
    }).join('');

    // Update color
    currentColor = hex;
    document.getElementById('color-picker').value = hex;
    updateColorSwatch();
}

function floodFill(startX, startY) {
    console.log('=== FLOOD FILL START ===');
    console.log('Click coordinates (48x48):', { startX, startY });
    console.log('Current color:', currentColor);

    // Get current canvas image data directly at 384x384
    const imageData = ctx.getImageData(0, 0, 384, 384);
    const data = imageData.data;

    // Convert coordinates to 384x384 space - use the center of the 8x8 block
    const targetX = startX * 8 + 4;
    const targetY = startY * 8 + 4;

    console.log('Target coordinates (384x384):', { targetX, targetY });

    // Get target color from the 384x384 canvas
    const targetColor = getPixelColor384(data, targetX, targetY);
    const fillColor = hexToRgb(currentColor);
    const fillColorWithAlpha = { r: fillColor.r, g: fillColor.g, b: fillColor.b, a: 255 };

    console.log('Target color:', targetColor);
    console.log('Fill color:', fillColorWithAlpha);

    // Use tolerance for color matching (allows for slight variations)
    const tolerance = 5; // Adjust this value as needed
    console.log('Using tolerance:', tolerance);
    console.log('Colors equal?', colorsEqual(targetColor, fillColorWithAlpha, tolerance));

    // Don't fill if target color is the same as fill color
    if (colorsEqual(targetColor, fillColorWithAlpha, tolerance)) {
        console.log('Target color same as fill color, skipping');
        return;
    }

    // Fill the entire 8x8 block for each logical pixel
    let pixelsChanged = 0;

    // Simple approach: fill 8x8 blocks instead of individual pixels
    const visited = new Set();
    const stack = [{ x: startX, y: startY }];

    while (stack.length > 0) {
        const { x, y } = stack.pop();

        // Check bounds in 48x48 space
        if (x < 0 || x >= 48 || y < 0 || y >= 48) continue;

        const key = `${x},${y}`;
        if (visited.has(key)) continue;
        visited.add(key);

        // Check the center pixel of this 8x8 block
        const blockCenterX = x * 8 + 4;
        const blockCenterY = y * 8 + 4;
        const currentPixelColor = getPixelColor384(data, blockCenterX, blockCenterY);

        if (!colorsEqual(currentPixelColor, targetColor, tolerance)) continue;

        // Fill the entire 8x8 block
        for (let dy = 0; dy < 8; dy++) {
            for (let dx = 0; dx < 8; dx++) {
                const pixelX = x * 8 + dx;
                const pixelY = y * 8 + dy;
                if (pixelX < 384 && pixelY < 384) {
                    setPixelColor384(data, pixelX, pixelY, fillColorWithAlpha);
                    pixelsChanged++;
                }
            }
        }

        // Add neighboring 48x48 blocks to stack
        stack.push({ x: x + 1, y });
        stack.push({ x: x - 1, y });
        stack.push({ x, y: y + 1 });
        stack.push({ x, y: y - 1 });
    }

    console.log('Pixels changed:', pixelsChanged);
    console.log('=== FLOOD FILL END ===');

    // Put modified image data back to canvas
    ctx.putImageData(imageData, 0, 0);

    // Update preview and mark as edited
    updatePreview();
    markImageAsEdited();
    scheduleAutoSave();
}

// Helper functions
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

function getPixelColor(data, x, y, width) {
    const index = (y * width + x) * 4;
    return {
        r: data[index],
        g: data[index + 1],
        b: data[index + 2],
        a: data[index + 3]
    };
}

function setPixelColor(data, x, y, width, color) {
    const index = (y * width + x) * 4;
    data[index] = color.r;
    data[index + 1] = color.g;
    data[index + 2] = color.b;
    data[index + 3] = 255; // Full opacity
}

function getPixelColor384(data, x, y) {
    const index = (y * 384 + x) * 4;
    return {
        r: data[index],
        g: data[index + 1],
        b: data[index + 2],
        a: data[index + 3]
    };
}

function setPixelColor384(data, x, y, color) {
    const index = (y * 384 + x) * 4;
    data[index] = color.r;
    data[index + 1] = color.g;
    data[index + 2] = color.b;
    data[index + 3] = color.a;
}

function colorsEqual(color1, color2, tolerance = 0) {
    const rDiff = Math.abs(color1.r - color2.r);
    const gDiff = Math.abs(color1.g - color2.g);
    const bDiff = Math.abs(color1.b - color2.b);
    const aDiff = Math.abs(color1.a - color2.a);

    return rDiff <= tolerance && gDiff <= tolerance && bDiff <= tolerance && aDiff <= tolerance;
}

function markImageAsEdited() {
    if (currentImage === null) return;

    loadedImages[currentImage].edited = true;

    // Update thumbnail
    const thumbnails = document.querySelectorAll('.thumbnail');
    if (thumbnails[currentImage]) {
        thumbnails[currentImage].classList.add('edited');
    }

    editCount++;
}

function scheduleAutoSave() {
    // Clear existing timeout
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }

    // Schedule save after 2 seconds or 10 edits
    const delay = editCount >= 10 ? 0 : 2000;
    saveTimeout = setTimeout(() => {
        saveCurrentImage();
        editCount = 0;
    }, delay);
}

async function saveCurrentImage() {
    if (currentImage === null || !currentProject) return;

    try {
        // Get 48x48 image data
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = 48;
        tempCanvas.height = 48;
        const tempCtx = tempCanvas.getContext('2d');
        tempCtx.imageSmoothingEnabled = false;
        tempCtx.drawImage(canvas, 0, 0, 48, 48);

        // Convert to PNG buffer
        const dataUrl = tempCanvas.toDataURL('image/png');
        const base64Data = dataUrl.replace(/^data:image\/png;base64,/, '');
        const buffer = Buffer.from(base64Data, 'base64');

        // Update database
        const filename = loadedImages[currentImage].filename;
        await currentProject.updateImage(filename, buffer);

        // Update loaded image data
        loadedImages[currentImage].currentData = buffer;
        loadedImages[currentImage].element.src = dataUrl;

        // Update thumbnail
        updateSidebar();

        // Save to file system
        const projectInfo = await currentProject.getProjectInfo();
        if (projectInfo && projectInfo.folder_path) {
            const filePath = path.join(projectInfo.folder_path, filename);
            fs.writeFileSync(filePath, buffer);
        }

    } catch (error) {
        console.error('Error saving image:', error);
    }
}

async function revertCurrentImage() {
    if (currentImage === null || !currentProject) return;

    const filename = loadedImages[currentImage].filename;

    // Revert in database
    await currentProject.revertImage(filename);

    // Get original data
    const imageRecord = await currentProject.getImage(filename);
    if (imageRecord) {
        // Update loaded image
        const dataUrl = 'data:image/png;base64,' + imageRecord.original_data.toString('base64');
        loadedImages[currentImage].element.src = dataUrl;
        loadedImages[currentImage].currentData = imageRecord.original_data;
        loadedImages[currentImage].edited = false;

        // Reload canvas
        loadedImages[currentImage].element.onload = () => {
            loadImageToCanvas(loadedImages[currentImage]);
            updateSidebar();
        };

        // Save to file system
        const projectInfo = await currentProject.getProjectInfo();
        if (projectInfo && projectInfo.folder_path) {
            const filePath = path.join(projectInfo.folder_path, filename);
            fs.writeFileSync(filePath, imageRecord.original_data);
        }
    }
}
